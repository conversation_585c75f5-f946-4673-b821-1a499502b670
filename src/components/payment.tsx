"use client";
import { initializePaddle, Paddle } from "@paddle/paddle-js";
import { useEffect, useState } from "react";
import { Button } from "./ui/button"
import Link from "next/link"

const Payment = () => {
    const [paddle, setPaddle] = useState<Paddle>()

    useEffect(() => {
        initializePaddle({
            environment: "sandbox",
            token: process.env.NEXT_PUBLIC_PADDLE_CLIENT_TOKEN || "",
        }).then((paddle) => {
            setPaddle(paddle)
        })
    }, [])
    const priceId = "pri_01jza23mbtxwejg1ma9a5nrf1h"
    const frequency = "monthly"
    
    const handlePayment = () => {
        if (!paddle) return

        paddle.Checkout.open({
          items: [
            {
              priceId: "pri_01jza23mbtxwejg1ma9a5nrf1h",
            },
          ],
          settings: {
            displayMode: "overlay",
            theme: "dark",
            successUrl: "http://localhost:3000/success",
          },
        })
    }
    return (
      <div className="flex flex-col items-center justify-center gap-4 p-4">
        <Button variant="default">
          <Link href={`/checkout/${priceId}`}>Get started</Link>
        </Button>
      </div>
    )
}

export default Payment;