"use client"
import { <PERSON><PERSON> } from "./ui/button"
import React, { useTransition } from "react"
import { signinWithAuth } from "@/actions/supabaseUser_action"

const LoginGithub = () => {
  const [isPending, startTransition] = useTransition()

  const handleGithubLogin = () => {
    startTransition(async () => {
      await signinWithAuth("github")
    })
  }

  return (
    <Button
      onClick={handleGithubLogin}
      disabled={isPending}
      type="button"
      variant="outline"
      className="w-full"
    >
      <svg
        viewBox="0 0 128 128"
        xmlns="http://www.w3.org/2000/svg"
        role="img"
        aria-label="GitHub icon"
      >
        <g id="Social_Icons">
          <g id="_x31__stroke">
            <g id="Github_1_">
              <rect fill="none" height="128" width="128" />
              <path
                fill="#3E75C3"
                d="M63.996,1.333C28.656,1.333,0,30.099,0,65.591
              c0,28.384,18.336,52.467,43.772,60.965c3.2,0.59,4.368-1.394,4.368-3.096
              c0-1.526-0.056-5.566-0.088-10.927c-17.804,3.883-21.56-8.614-21.56-8.614
              c-2.908-7.421-7.104-9.397-7.104-9.397c-5.812-3.988,0.44-3.907,0.44-3.907
              c6.42,0.454,9.8,6.622,9.8,6.622c5.712,9.819,14.98,6.984,18.628,5.337
              c0.58-4.152,2.236-6.984,4.064-8.59c-14.212-1.622-29.152-7.132-29.152-31.753
              c0-7.016,2.492-12.75,6.588-17.244c-0.66-1.626-2.856-8.156,0.624-17.003
              c0,0,5.376-1.727,17.6,6.586c5.108-1.426,10.58-2.136,16.024-2.165
              c5.436,0.028,10.912,0.739,16.024,2.165c12.216-8.313,17.58-6.586,17.58-6.586
              c3.492,8.847,1.296,15.377,0.636,17.003c4.104,4.494,6.58,10.228,6.58,17.244
              c0,24.681-14.964,30.115-29.22,31.705c2.296,1.984,4.344,5.903,4.344,11.899
              c0,8.59-0.08,15.517-0.08,17.626c0,1.719,1.152,3.719,4.4,3.088
              C109.68,118.034,128,93.967,128,65.591C128,30.099,99.344,1.333,63.996,1.333"
              />
            </g>
          </g>
        </g>
      </svg>
      {isPending ? "Redirecting..." : "Login with Github"}
    </Button>
  )
}

export default LoginGithub
